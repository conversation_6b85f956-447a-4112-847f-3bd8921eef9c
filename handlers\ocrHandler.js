const { QuickDB } = require("quick.db");
const db = new QuickDB();
const axios = require("axios");
const fs = require("fs");
const path = require("path");
const { generateApiUrl } = require("../utils/urlSigner");
const { clearSessionTimeout } = require("../utils/sessionTimeouts");

module.exports = async (client, blobClient, event) => {
  let userId = event.source.userId;
  if (event.source.type === "group") {
    userId = `${event.source.groupId}:${event.source.userId}`;
  }
  const isOcrActive = await db.get(`ocr_${userId}_active`);

  if (!isOcrActive) {
    return false;
  }

  if (
    event.message.type !== "image" ||
    event.message.contentProvider.type !== "line"
  ) {
    return false;
  }

  clearSessionTimeout(userId, "ocr");
  await db.delete(`ocr_${userId}_active`);
  const question =
    (await db.get(`ocr_question_${userId}`)) ||
    "Describe this image, and reply in unformatted plain text ";
  await db.delete(`ocr_question_${userId}`);

  let stream = await blobClient.getMessageContent(event.message.id);
  const chunks = [];
  stream.on("data", (chunk) => chunks.push(chunk));

  stream.on("end", async () => {
    const buffer = Buffer.concat(chunks);
    const timestamp = Date.now();
    const dirPath = path.join(__dirname, "../static/downloads");
    const filePath = path.join(dirPath, `${timestamp}.jpg`);

    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
    }

    fs.writeFileSync(filePath, buffer);
    console.log(`Gambar berhasil disimpan di ${filePath}`);

    const imageUrl = generateApiUrl(`/downloads/${timestamp}.jpg`);

    try {
      const chatHistory = (await db.get(`chatHistory_${userId}`)) || [];
      const response = await axios.post(
        "http://127.0.0.1:5000/analyze_image",
        {
          image_url: imageUrl,
          question: question,
          
        }
      );

      const answer = response.data.answer;

      chatHistory.push({ role: "user", content: question });
      chatHistory.push({ role: "assistant", content: answer });
      await db.set(`chatHistory_${userId}`, chatHistory);

      await client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: `${answer}`,
            quoteToken: event.message.quoteToken,
          },
        ],
      });
    } catch (error) {
      console.error("Error analyzing image:", error);
      await client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "Terjadi kesalahan saat menganalisis gambar.",
            quoteToken: event.message.quoteToken,
          },
        ],
      });
    } finally {
      fs.unlink(filePath, (err) => {
        if (err) console.error(`[ Failed ] to delete image file: ${filePath}`, err);
        else console.log(`Successfully deleted image file: ${filePath}`);
      });
    }
  });

  stream.on("error", async (err) => {
    console.error("Error downloading content:", err);
    await client.replyMessage({
      replyToken: event.replyToken,
      messages: [
        {
          type: "text",
          text: "An error occurred while downloading the image.",
          quoteToken: event.message.quoteToken,
        },
      ],
    });
  });

  return true; // OCR handled
};