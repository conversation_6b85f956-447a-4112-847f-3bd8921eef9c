from g4f.client import Client
from g4f.Provider import <PERSON><PERSON><PERSON>, Gemini, CopilotAccount
import os

import g4f.debug
g4f.debug.logging = True

client = Client(provider=CopilotAccount)
response = client.chat.completions.create(
    # Try "gpt-4o", "deepseek-v3", etc.
    messages=[
        {
         "role": "system", 
         "content": "You are a helpful assistant named r<PERSON><PERSON>."
        },
        {      
         "role": "user", 
         "content": "what is your name?"}
        ],
    web_search=False
    
)
print(response.choices[0].message.content)